<template>
  <div class="balance-query-component">
    <div class="header">
      <el-button 
        type="primary" 
        :icon="ArrowLeft" 
        @click="handleBack"
        class="back-button"
      >
        返回
      </el-button>
      <h1>科目余额查询</h1>
    </div>

    <div class="query-panel">
      <el-button 
        type="primary" 
        :icon="Search" 
        @click="executeQuery"
        :loading="loading"
      >
        查询
      </el-button>
    </div>

    <div class="result-panel">
      <div class="result-header">
        <span class="result-count">
          共找到 {{ resultCount }} 条记录
        </span>
      </div>

      <VTableComponent
        v-if="hasResults"
        :data="tableData"
        :width="1200"
        :height="500"
        class="query-table"
      />
      
      <div v-else-if="!loading && hasQueried" class="no-data">
        <el-empty description="暂无数据" />
      </div>
      
      <div v-else-if="!hasQueried" class="no-query">
        <el-empty description="输入条件后进行查询" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Search } from '@element-plus/icons-vue'
import VTableComponent from './VTableComponent.vue'

const emit = defineEmits(['back'])

const loading = ref(false)
const hasQueried = ref(false)
const hasResults = ref(false)
const resultCount = ref(0)
const tableData = ref([])

function handleBack() {
  emit('back')
}

async function executeQuery() {
  loading.value = true
  try {
    const response = await fetch('http://localhost:8000/api/balance-sheet-query', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ queryType: 'balance' })
    })
    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
    }
    const result = await response.json()
    if (result.success && result.data && result.data.length > 0) {
      const headers = result.data[0]
      const rows = result.data.slice(1)

      // 将表格数据转换为VTableComponent需要的格式
      const tableDataFormatted = [headers, ...rows]
      
      tableData.value = tableDataFormatted
      resultCount.value = rows.length
      hasResults.value = true
    } else {
      throw new Error(result.message || '查询返回空结果')
    }
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请重试')
  } finally {
    loading.value = false
    hasQueried.value = true
  }
}
</script>

<style scoped>
.balance-query-component {
  width: 100%;
  padding: 10px;
  background-color: #f9fafb;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.header h1 {
  margin: 0 0 0 10px;
  font-size: 24px;
}

.query-panel {
  margin-bottom: 20px;
}

.result-panel {
  width: 100%;
  background-color: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
}

.result-count {
  font-weight: bold;
  color: #333;
}

.no-data, .no-query {
  text-align: center;
  padding: 20px 0;
}
</style>

