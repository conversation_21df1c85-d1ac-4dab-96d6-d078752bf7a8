# UniversalTableComponent 通用表格组件

基于 Univer 的通用表格组件，参考 SalaryTax2View 实现，支持多表格管理和字符型数字处理。

## 功能特性

- ✅ 支持多个表格（工作表）同时管理
- ✅ 自动处理身份证号、工号等字符型数字，防止 Excel 自动转换
- ✅ 支持父组件数据交换（输入/输出）
- ✅ 空表格初始化，支持动态数据加载
- ✅ 响应式设计，适配移动端
- ✅ 完整的错误处理和加载状态
- ✅ 支持数据导出和刷新

## 使用方法

### 1. 基本用法

```vue
<template>
  <UniversalTableComponent
    :data-provider="dataProvider"
    :initial-data="initialData"
    workbook-name="我的工作簿"
    @data-change="handleDataChange"
    @initialized="handleInitialized"
    @error="handleError"
  />
</template>

<script setup>
import UniversalTableComponent from '@/components/UniversalTableComponent.vue'

// 初始数据
const initialData = {
  '员工表': [
    ['工号', '姓名', '身份证号'],
    ['E001', '张三', '110101199001011234']
  ],
  '薪资表': [
    ['工号', '基本工资', '奖金'],
    ['E001', 15000, 3000]
  ]
}

// 数据提供函数
const dataProvider = async () => {
  const response = await fetch('/api/table-data')
  return await response.json()
}

// 事件处理
const handleDataChange = (data) => {
  console.log('表格数据变化:', data)
  // 可以在这里保存数据到后端
}
</script>
```

### 2. Props 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `dataProvider` | `() => Promise<Record<string, any[][]>>` | - | 数据获取函数，返回表名到二维数组的映射 |
| `initialData` | `Record<string, any[][]>` | - | 初始数据，格式为 `{ 表名: 二维数组 }` |
| `workbookName` | `string` | `'通用表格'` | 工作簿名称 |

### 3. Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `dataChange` | `data: Record<string, any[][]>` | 表格数据发生变化时触发 |
| `initialized` | - | 组件初始化完成时触发 |
| `error` | `error: string` | 发生错误时触发 |

### 4. 暴露的方法

通过 `ref` 可以调用以下方法：

```vue
<template>
  <UniversalTableComponent ref="tableRef" />
</template>

<script setup>
const tableRef = ref()

// 加载数据
tableRef.value.loadData({
  '新表格': [['列1', '列2'], ['数据1', '数据2']]
})

// 获取所有表格数据
const allData = tableRef.value.getAllTableData()

// 刷新数据（调用 dataProvider）
tableRef.value.refreshData()

// 导出数据（触发 dataChange 事件）
tableRef.value.exportData()
</script>
```

## 数据格式

### 输入数据格式

```typescript
type TableData = Record<string, any[][]>

// 示例
const data: TableData = {
  '表名1': [
    ['列1', '列2', '列3'],  // 表头
    ['数据1', '数据2', '数据3'],  // 数据行
    ['数据4', '数据5', '数据6']
  ],
  '表名2': [
    ['姓名', '身份证号'],
    ['张三', '110101199001011234']  // 身份证号会自动处理为文本格式
  ]
}
```

### 字符型数字处理

组件会自动识别以下列并处理为文本格式，防止 Excel 自动转换：

- 包含 `身份证号` 的列
- 包含 `证件号码` 的列  
- 包含 `银行卡号` 的列
- 包含 `工号` 的列
- 纯数字的列名

处理后的格式：
```javascript
{
  v: '110101199001011234',  // 值
  t: 1,                     // CellValueType.STRING
  s: null                   // 样式
}
```

## 完整示例

参考 `src/examples/UniversalTableExample.vue` 文件，包含：

- 多表格数据管理
- 异步数据加载
- 数据变化监听
- 错误处理
- 响应式界面

## 样式定制

组件使用 scoped CSS，可以通过以下方式定制样式：

```vue
<style>
/* 覆盖组件样式 */
.universal-table-component .control-panel {
  background: #your-color;
}

/* 或使用 CSS 变量 */
.universal-table-component {
  --primary-color: #your-color;
}
</style>
```

## 注意事项

1. **依赖要求**：需要安装 Univer 相关依赖包
2. **内存管理**：组件会在 `onBeforeUnmount` 时自动清理 Univer 实例
3. **数据量限制**：建议单个表格数据不超过 10000 行，以保证性能
4. **浏览器兼容性**：支持现代浏览器，IE 不支持

## 错误处理

组件内置了完整的错误处理机制：

- 初始化失败
- 数据加载失败  
- 数据格式错误
- 网络请求失败

所有错误都会通过 `error` 事件向父组件报告。

## 性能优化

- 使用虚拟滚动处理大数据量
- 懒加载工作表内容
- 防抖处理数据变化事件
- 内存泄漏防护