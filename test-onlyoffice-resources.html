<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnlyOffice 资源测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
        }
        .success { background: #4CAF50; }
        .error { background: #f44336; }
        .loading { background: #ff9800; }
        .test-button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #1976D2;
        }
        .log {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 10px;
            margin-top: 20px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>OnlyOffice 资源部署测试</h1>
        <p>此页面用于测试OnlyOffice相关资源是否正确部署</p>
        
        <div class="test-section">
            <h3>资源文件检查</h3>
            
            <div class="test-item">
                <span>主应用脚本 (app.js)</span>
                <span id="app-js-status" class="status loading">检查中...</span>
            </div>
            
            <div class="test-item">
                <span>SDK核心文件 (sdk-all-min.js)</span>
                <span id="sdk-status" class="status loading">检查中...</span>
            </div>
            
            <div class="test-item">
                <span>字体文件 (AllFonts.js)</span>
                <span id="fonts-status" class="status loading">检查中...</span>
            </div>
            
            <div class="test-item">
                <span>主样式文件 (main.css)</span>
                <span id="css-status" class="status loading">检查中...</span>
            </div>
            
            <div class="test-item">
                <span>应用样式文件 (app.css)</span>
                <span id="app-css-status" class="status loading">检查中...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>API可用性测试</h3>
            <button class="test-button" onclick="testAPI()">测试OnlyOffice API</button>
            <span id="api-status" class="status loading">未测试</span>
        </div>
        
        <div class="test-section">
            <h3>编辑器初始化测试</h3>
            <button class="test-button" onclick="testEditor()">测试编辑器初始化</button>
            <span id="editor-status" class="status loading">未测试</span>
            <div id="test-editor" style="width: 100%; height: 300px; border: 1px solid #ddd; margin-top: 10px; display: none;"></div>
        </div>
        
        <div class="log" id="log">
            <div>测试日志:</div>
        </div>
    </div>

    <script>
        function log(message) {
            const logElement = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logElement.innerHTML += `<div>[${time}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function setStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = text;
        }

        function checkResource(url, statusElementId, name) {
            return new Promise((resolve) => {
                const xhr = new XMLHttpRequest();
                xhr.open('HEAD', url, true);
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        setStatus(statusElementId, 'success', '✓ 可用');
                        log(`${name} - 资源可用`);
                        resolve(true);
                    } else {
                        setStatus(statusElementId, 'error', '✗ 不可用');
                        log(`${name} - 资源不可用 (状态: ${xhr.status})`);
                        resolve(false);
                    }
                };
                xhr.onerror = function() {
                    setStatus(statusElementId, 'error', '✗ 错误');
                    log(`${name} - 资源加载错误`);
                    resolve(false);
                };
                xhr.send();
            });
        }

        async function checkAllResources() {
            log('开始检查OnlyOffice资源...');
            
            const resources = [
                { url: '/web-apps/apps/spreadsheeteditor/main/app.js', id: 'app-js-status', name: '主应用脚本' },
                { url: '/sdkjs/cell/sdk-all-min.js', id: 'sdk-status', name: 'SDK核心文件' },
                { url: '/sdkjs/common/AllFonts.js', id: 'fonts-status', name: '字体文件' },
                { url: '/sdkjs/cell/css/main.css', id: 'css-status', name: '主样式文件' },
                { url: '/web-apps/apps/spreadsheeteditor/main/resources/css/app.css', id: 'app-css-status', name: '应用样式文件' }
            ];

            for (const resource of resources) {
                await checkResource(resource.url, resource.id, resource.name);
            }
            
            log('资源检查完成');
        }

        function testAPI() {
            log('开始测试OnlyOffice API...');
            
            if (typeof DocsAPI !== 'undefined') {
                setStatus('api-status', 'success', '✓ API可用');
                log('OnlyOffice API已加载并可用');
            } else {
                // 尝试加载API
                const script = document.createElement('script');
                script.src = '/web-apps/apps/spreadsheeteditor/main/app.js';
                script.onload = function() {
                    setTimeout(() => {
                        if (typeof DocsAPI !== 'undefined') {
                            setStatus('api-status', 'success', '✓ API可用');
                            log('OnlyOffice API加载成功');
                        } else {
                            setStatus('api-status', 'error', '✗ API不可用');
                            log('OnlyOffice API加载失败');
                        }
                    }, 1000);
                };
                script.onerror = function() {
                    setStatus('api-status', 'error', '✗ 加载失败');
                    log('OnlyOffice API脚本加载失败');
                };
                document.head.appendChild(script);
            }
        }

        function testEditor() {
            log('开始测试编辑器初始化...');
            
            if (typeof DocsAPI === 'undefined') {
                setStatus('editor-status', 'error', '✗ API未加载');
                log('编辑器测试失败: API未加载');
                return;
            }

            try {
                const editorContainer = document.getElementById('test-editor');
                editorContainer.style.display = 'block';
                
                const config = {
                    "document": {
                        "fileType": "xlsx",
                        "key": "test_" + Date.now(),
                        "title": "测试文档",
                        "permissions": {
                            "edit": true,
                            "download": true
                        }
                    },
                    "documentType": "cell",
                    "editorConfig": {
                        "mode": "edit",
                        "lang": "zh-CN"
                    },
                    "width": "100%",
                    "height": "300px",
                    "events": {
                        "onDocumentReady": function() {
                            setStatus('editor-status', 'success', '✓ 初始化成功');
                            log('编辑器初始化成功');
                        },
                        "onError": function(event) {
                            setStatus('editor-status', 'error', '✗ 初始化失败');
                            log('编辑器初始化失败: ' + JSON.stringify(event));
                        }
                    }
                };

                new DocsAPI.DocEditor("test-editor", config);
                log('编辑器创建命令已执行');
                
            } catch (error) {
                setStatus('editor-status', 'error', '✗ 异常');
                log('编辑器测试异常: ' + error.message);
            }
        }

        // 页面加载完成后自动检查资源
        window.onload = function() {
            log('页面加载完成，开始自动检查...');
            checkAllResources();
        };
    </script>
</body>
</html>
