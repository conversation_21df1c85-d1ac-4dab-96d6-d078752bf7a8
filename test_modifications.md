# 薪酬个税系统修改测试指南

## 测试环境准备

### 1. 启动后端服务
```bash
cd backend
python start.py
# 或者
python3 start.py
```

### 2. 启动前端服务
```bash
npm run dev
# 或者
yarn dev
```

## 功能测试

### 1. 专项附加扣除导入测试

#### 测试步骤：
1. 打开薪酬个税管理系统 (SalaryTax2View)
2. 准备一个包含专项附加扣除数据的Excel文件，表头应包含：
   - 工号
   - 姓名
   - 证件类型
   - 证件号码
   - 所得期间起
   - 所得期间止
   - 本期收入
   - 累计子女教育
   - 累计继续教育
   - 累计住房贷款利息
   - 累计住房租金
   - 累计赡养老人
   - 累计3岁以下婴幼儿照护
   - 累计个人养老金

3. 点击"导入专项附加"按钮
4. 选择Excel文件
5. 选择导入模式（覆盖导入或追加导入）
6. 验证数据是否正确导入到"累计专项附加扣除表"工作表

#### 预期结果：
- 身份证号列应以文本格式显示，不会被转换为科学计数法
- 覆盖导入会清空现有数据并替换为新数据
- 追加导入会在现有数据后添加新数据
- 表头样式正确（粗体、灰色背景）

### 2. 社保公积金回摊计算测试

#### 测试步骤：
1. 确保"算税底稿"工作表有数据，包含必要字段：
   - 月份、身份证号、姓名、社保缴纳单位、公积金缴纳单位、年金缴纳单位
   - 本期收入、基本养老保险费、住房公积金、基本医疗保险费等

2. 确保"社保公积金实际缴纳表"工作表有数据，包含：
   - 月份、代缴单位、个人公积金、个人养老、个人医疗等
   - 单位公积金、单位养老、单位医疗、单位失业、单位工伤等

3. 点击"计算社保公积金回摊"按钮
4. 等待后台计算完成
5. 检查"薪酬总表(无一次性年终及包干)"工作表是否更新

#### 预期结果：
- 系统应成功发送数据到后台API
- 后台应返回计算好的回摊数据
- "薪酬总表(无一次性年终及包干)"工作表应显示完整的回摊计算结果
- 包含个人回摊、单位分摊、总成本等字段

### 3. 构建财务模板测试

#### 测试步骤：
1. 确保已完成社保公积金回摊计算
2. 点击"构建财务一体化计提发放模板(社保公积金表)"按钮
3. 等待后台处理完成
4. 检查返回的模板数据

#### 预期结果：
- 系统应成功汇总薪酬总表数据
- 按月份和缴纳单位分类汇总
- 返回财务一体化模板数据结构

## API测试

### 1. 社保公积金回摊计算API
```bash
curl -X POST http://localhost:8000/api/calculate-social-security-allocation \
  -H "Content-Type: application/json" \
  -d '{
    "taxCalculationData": [["表头"], ["数据行"]],
    "socialSecurityData": [["表头"], ["数据行"]],
    "year": "2025"
  }'
```

### 2. 构建财务模板API
```bash
curl -X POST http://localhost:8000/api/build-finance-template \
  -H "Content-Type: application/json" \
  -d '{
    "adjustmentData": [["表头"], ["数据行"]],
    "year": "2025",
    "templateType": "socialSecurityAndHousingFund"
  }'
```

## 常见问题排查

### 1. 导入失败
- 检查Excel文件格式是否正确
- 确认表头字段名称是否匹配
- 检查文件是否包含数据

### 2. 回摊计算失败
- 确认"算税底稿"和"社保公积金实际缴纳表"数据完整
- 检查后台服务是否正常运行
- 查看浏览器控制台错误信息

### 3. 身份证号显示异常
- 确认导入时身份证号列被正确识别
- 检查processIdColumnsAsText函数是否正常工作

### 4. 后端API错误
- 检查后端服务器日志
- 确认数据格式符合API要求
- 验证必要字段是否存在

## 数据验证

### 1. 身份证号格式
- 应显示为完整的18位数字
- 不应出现科学计数法（如1.23E+17）
- 文本格式应正确保持

### 2. 回摊计算准确性
- 个人回摊金额应与社保公积金实际缴纳表匹配
- 单位分摊金额应正确计算
- 总成本 = 收入 + 个人回摊 + 单位分摊 + 个税

### 3. 数据完整性
- 导入后数据行数应正确
- 所有必要字段应有值
- 表头样式应正确应用
