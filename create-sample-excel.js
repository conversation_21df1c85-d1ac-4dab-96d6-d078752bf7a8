// 创建示例 Excel 文件用于测试导入功能
import ExcelJS from 'exceljs';
import fs from 'fs';

async function createSampleExcel() {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('示例数据');

    // 设置表头
    const headers = ['ID', '姓名', '部门', '职位', '薪资', '入职日期', '状态'];
    worksheet.addRow(headers);

    // 添加示例数据
    const sampleData = [
        [1, '张三', '技术部', '高级工程师', 15000, '2023-01-15', '在职'],
        [2, '李四', '市场部', '市场经理', 12000, '2023-02-20', '在职'],
        [3, '王五', '人事部', '人事专员', 8000, '2023-03-10', '在职'],
        [4, '赵六', '财务部', '会计师', 10000, '2023-04-05', '在职'],
        [5, '钱七', '技术部', '前端工程师', 13000, '2023-05-12', '离职'],
        [6, '孙八', '市场部', '销售代表', 9000, '2023-06-18', '在职'],
        [7, '周九', '技术部', '后端工程师', 14000, '2023-07-22', '在职'],
        [8, '吴十', '人事部', '招聘专员', 7500, '2023-08-30', '在职'],
    ];

    sampleData.forEach(row => {
        worksheet.addRow(row);
    });

    // 设置列宽
    worksheet.columns = [
        { width: 8 },   // ID
        { width: 12 },  // 姓名
        { width: 12 },  // 部门
        { width: 15 },  // 职位
        { width: 10 },  // 薪资
        { width: 15 },  // 入职日期
        { width: 10 },  // 状态
    ];

    // 设置表头样式
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
        cell.font = { bold: true, color: { argb: 'FFFFFF' } };
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '4472C4' }
        };
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
        cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
        };
    });

    // 设置数据行样式
    for (let i = 2; i <= worksheet.rowCount; i++) {
        const row = worksheet.getRow(i);
        row.eachCell({ includeEmpty: true }, (cell) => {
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' }
            };
            cell.alignment = { horizontal: 'left', vertical: 'middle' };
        });
    }

    // 保存文件
    await workbook.xlsx.writeFile('sample-data.xlsx');
    console.log('示例 Excel 文件已创建: sample-data.xlsx');
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
    createSampleExcel().catch(console.error);
}

export default createSampleExcel;
