# NativeTableComponent 原生表格组件

## 概述

NativeTableComponent 是一个完全原生实现的 Vue 3 表格组件，旨在替代基于 @visactor/vtable 的 VTableComponent。它提供了与 VTableComponent 完全相同的 API 接口，支持无缝替换，同时具有更好的性能和更小的包体积。

## 特性

- ✅ **无外部依赖**: 不依赖任何第三方表格库，减少包体积
- ✅ **API 兼容**: 与 VTableComponent 完全相同的 API，支持无缝替换
- ✅ **高性能**: 原生 HTML 表格实现，渲染性能更好
- ✅ **完整功能**: 支持编辑、筛选、复制粘贴、导入导出等所有功能
- ✅ **响应式设计**: 自动适应容器尺寸变化
- ✅ **列宽调整**: 支持拖拽调整列宽和自动适应内容
- ✅ **Excel 兼容**: 支持 Excel 导入导出，保持格式和公式
- ✅ **键盘操作**: 支持 Ctrl+C/V 复制粘贴，Enter/Esc 编辑控制
- ✅ **多条件筛选**: 支持复杂的筛选条件和逻辑操作符
- ✅ **样式可定制**: CSS 样式完全可控，易于主题定制

## 安装依赖

```bash
# 只需要 ExcelJS 用于 Excel 导入导出功能
npm install exceljs
```

## 基础用法

```vue
<template>
  <NativeTableComponent
    :data="tableData"
    :width="800"
    :height="400"
    :show-filter="true"
    :editable="true"
    :enable-copy-paste="true"
    :auto-width="true"
    @data-change="handleDataChange"
    @cell-edit="handleCellEdit"
  />
</template>

<script setup>
import NativeTableComponent from '@/components/NativeTableComponent.vue'

const tableData = ref([
  ['姓名', '年龄', '城市'],
  ['张三', 28, '北京'],
  ['李四', 32, '上海']
])

const handleDataChange = (newData) => {
  console.log('数据变化:', newData)
}

const handleCellEdit = (event) => {
  console.log('单元格编辑:', event)
}
</script>
```

## 替换 VTableComponent

### 1. 导入组件

```javascript
// 原来的导入
import VTableComponent from '@/components/VTableComponent.vue'

// 替换为
import NativeTableComponent from '@/components/NativeTableComponent.vue'
```

### 2. 模板中使用

```vue
<!-- 原来的使用 -->
<VTableComponent
  :data="tableData"
  :width="800"
  :height="400"
  :editable="true"
  @data-change="handleDataChange"
/>

<!-- 替换为（API完全相同） -->
<NativeTableComponent
  :data="tableData"
  :width="800"
  :height="400"
  :editable="true"
  @data-change="handleDataChange"
/>
```

### 3. 方法调用

```javascript
// 所有方法调用保持不变
const tableRef = ref()

// 获取数据
const data = tableRef.value.getData()

// 导出Excel
tableRef.value.exportExcel()

// 添加行
tableRef.value.addRow()
```

## Props 配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `data` | Array | `[]` | 二维数组数据，第一行为标题 |
| `width` | Number | `600` | 表格宽度 |
| `height` | Number | `300` | 表格高度 |
| `showFilter` | Boolean | `true` | 是否显示筛选面板 |
| `editable` | Boolean | `true` | 是否启用编辑功能 |
| `enableCopyPaste` | Boolean | `true` | 是否启用复制粘贴 |
| `autoWidth` | Boolean | `true` | 是否自动调整列宽 |
| `enableExcelImport` | Boolean | `false` | 是否启用Excel导入 |
| `enablePushUpdate` | Boolean | `false` | 是否启用后台推送更新 |
| `pushUpdateEndpoint` | String | `''` | 后台推送更新的API端点 |
| `tableName` | String | `''` | 表名（用于后台推送） |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `data-change` | `newData` | 数据变化时触发 |
| `cell-edit` | `{ col, row, newValue, oldValue }` | 单元格编辑时触发 |
| `copy` | `{ data }` | 复制操作时触发 |
| `paste` | `{ data, startRow, startCol }` | 粘贴操作时触发 |
| `cell-select` | `{ row, col }` | 单元格选择时触发 |

## 方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `getData()` | - | `Array` | 获取当前表格数据 |
| `setData(newData)` | `Array` | - | 设置表格数据 |
| `addRow()` | - | - | 添加新行 |
| `deleteRows()` | - | - | 删除选中行 |
| `exportExcel()` | - | - | 导出Excel文件 |
| `exportCSV()` | - | - | 导出CSV文件 |
| `copy()` | - | - | 复制选中内容 |
| `paste()` | - | - | 粘贴内容 |
| `autoFitAllColumnWidth()` | - | - | 自动调整所有列宽 |
| `selectCell(row, col)` | `Number, Number` | - | 选择指定单元格 |
| `startEdit(row, col)` | `Number, Number` | - | 开始编辑指定单元格 |

## 功能特性

### 编辑功能
- 双击单元格进入编辑模式
- 按 Enter 确认编辑，Esc 取消编辑
- 支持键盘导航（方向键移动）

### 复制粘贴
- 支持 Ctrl+C 复制选中内容
- 支持 Ctrl+V 粘贴内容
- 兼容 Excel 等外部应用的数据格式

### 筛选功能
- 支持多条件筛选
- 支持 AND/OR 逻辑操作符
- 支持包含、等于、开头是、结尾是、大于、小于等操作符

### 列宽调整
- 拖拽表头边缘调整列宽
- 自动根据内容计算最佳列宽
- 支持最小/最大宽度限制

### 导入导出
- Excel 导入支持追加和覆盖模式
- Excel 导出保持格式和样式
- CSV 导出支持中文编码

## 性能优势

1. **包体积减少**: 移除 @visactor/vtable 依赖，减少约 2MB 包体积
2. **渲染性能**: 原生 HTML 表格，浏览器优化更好
3. **内存占用**: 更少的 JavaScript 对象，内存占用更低
4. **启动速度**: 减少第三方库加载时间

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 注意事项

1. 数据数组的第一行必须是标题行
2. 所有数据行的列数应该与标题行保持一致
3. 大量数据时建议启用虚拟滚动（通过 CSS 优化）
4. 复制粘贴功能需要 HTTPS 环境或 localhost

## 示例项目

查看以下文件获取完整的使用示例：
- `src/test/NativeTableTest.vue` - 功能测试示例
- `src/examples/NativeTableExample.vue` - 使用说明示例
