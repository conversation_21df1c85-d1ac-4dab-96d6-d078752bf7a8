<template>
  <div class="home-container">
    <!-- 头部标题 -->
    <div class="header">
      <h1 class="title">信小财RPA财务机器人</h1>
      <div class="header-actions">
        <el-button type="primary" size="small" @click="openManual">
          <el-icon>
            <Document />
          </el-icon>
          操作手册
        </el-button>
      </div>
    </div>

    <!-- 功能模块网格 -->
    <div class="modules-grid">
      <!-- 第一行 -->
      <div class="module-card" @click="navigateTo('/statistics')">
        <div class="module-icon statistics">
          <div class="icon-wrapper">
            <el-icon>
              <DataLine />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>统计大师</h3>
          <p>快速生成各类统计报表</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/auto-voucher')">
        <div class="module-icon auto-voucher">
          <div class="icon-wrapper">
            <el-icon>
              <Document />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>自动制证</h3>
          <p>一键完成各类凭证制作</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/fund-manager')">
        <div class="module-icon fund">
          <div class="icon-wrapper">
            <el-icon>
              <Wallet />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>资金协管</h3>
          <p>智能化资金管理专家</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/smart-audit')">
        <div class="module-icon audit">
          <div class="icon-wrapper">
            <el-icon>
              <Search />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>智能稽核</h3>
          <p>智能化单据审核验证</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/one-click-close')">
        <div class="module-icon close">
          <div class="icon-wrapper">
            <el-icon><Select /></el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>一键结账</h3>
          <p>快速完成月末结账处理</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/smart-tax')">
        <div class="module-icon tax">
          <div class="icon-wrapper">
            <el-icon>
              <Files />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>智慧税务</h3>
          <p>智能化税务处理专家</p>
        </div>
      </div>

      <!-- 第二行 -->
      <div class="module-card" @click="navigateTo('/quick-fill')">
        <div class="module-icon quick-fill">
          <div class="icon-wrapper">
            <el-icon>
              <Edit />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>速填精灵</h3>
          <p>精确快速的数据填充</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/report-assistant')">
        <div class="module-icon assistant">
          <div class="icon-wrapper">
            <el-icon>
              <Histogram />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>报表助手</h3>
          <p>生成各类分析报表</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/electronic-archive')">
        <div class="module-icon archive">
          <div class="icon-wrapper">
            <el-icon>
              <Folder />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>电子档案</h3>
          <p>电子文档管理系统</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/system-sync')">
        <div class="module-icon sync">
          <div class="icon-wrapper">
            <el-icon>
              <Link />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>系统协同</h3>
          <p>多系统协同工作平台</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/settings')">
        <div class="module-icon settings">
          <div class="icon-wrapper">
            <el-icon>
              <Tools />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>设置管理</h3>
          <p>用于参数设置及系统配置</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/others')">
        <div class="module-icon others">
          <div class="icon-wrapper">
            <el-icon>
              <Grid />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>其他</h3>
          <p>更多功能和工具</p>
        </div>
      </div>
    </div>

    <!-- 底部信息面板 -->
    <div class="info-panels">
      <div class="info-panel">
        <h4>
          <el-icon>
            <List />
          </el-icon>
          系统状态
        </h4>
        <ul>
          <li>RPA数据自动采集</li>
          <li>系统运行正常</li>
          <li>数据库连接正常</li>
        </ul>
      </div>

      <div class="info-panel">
        <h4>
          <el-icon>
            <InfoFilled />
          </el-icon>
          软件信息
        </h4>
        <div class="software-info">
          <p>本地版本：<span class="version">{{ localVersion.version }}</span></p>
          <p>版本状态：<span class="status">{{ localVersion.status }}</span></p>
          <p>远程版本：<span class="remote-version">{{ remoteVersion.versionCode }}</span></p>
          <p>版本描述：<span class="version-desc">{{ remoteVersion.versionDesc }}</span></p>
        </div>
      </div>

      <div class="info-panel">
        <h4>
          <el-icon>
            <Calendar />
          </el-icon>
          最近活动
        </h4>
        <div class="recent-activity">
          <div v-for="(activity, index) in recentActivities" :key="`${activity.time}-${index}`" class="activity-item">
            <span class="activity-name">{{ activity.name }}</span>
            <span class="activity-time">{{ activity.time }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

const router = useRouter()

// 从 sessionStorage 加载版本信息
const loadVersionInfo = () => {
  try {
    const localStored = sessionStorage.getItem('localVersion')
    const remoteStored = sessionStorage.getItem('remoteVersion')

    return {
      local: localStored ? JSON.parse(localStored) : { version: '加载中...', status: '检测中...' },
      remote: remoteStored ? JSON.parse(remoteStored) : { versionCode: '加载中...', versionDesc: '检测中...' }
    }
  } catch (error) {
    console.error('加载版本信息失败:', error)
    return {
      local: { version: '加载中...', status: '检测中...' },
      remote: { versionCode: '加载中...', versionDesc: '检测中...' }
    }
  }
}

const versionInfo = loadVersionInfo()

// 版本信息响应式数据
const localVersion = ref(versionInfo.local)
const remoteVersion = ref(versionInfo.remote)

// 功能名称映射
const moduleNames = {
  '/statistics': '统计大师',
  '/auto-voucher': '自动制证',
  '/fund-manager': '资金协管',
  '/smart-audit': '智能稽核',
  '/one-click-close': '一键结账',
  '/smart-tax': '智慧税务',
  '/quick-fill': '速填精灵',
  '/report-assistant': '报表助手',
  '/electronic-archive': '电子档案',
  '/system-sync': '系统协同',
  '/settings': '设置管理',
  '/others': '其他'
}

// 从 sessionStorage 加载活动记录
const loadRecentActivities = () => {
  try {
    const stored = sessionStorage.getItem('recentActivities')
    if (stored) {
      return JSON.parse(stored)
    }
  } catch (error) {
    console.error('加载活动记录失败:', error)
  }

  // 如果没有存储的记录，返回默认记录
  const now = new Date()
  const timeString = now.getHours().toString().padStart(2, '0') + ':' +
    now.getMinutes().toString().padStart(2, '0') + ':' +
    now.getSeconds().toString().padStart(2, '0')

  return [{ name: '客户端启动', time: timeString }]
}

// 最近活动记录
const recentActivities = ref(loadRecentActivities())

// 保存活动记录到 sessionStorage
const saveRecentActivities = () => {
  try {
    sessionStorage.setItem('recentActivities', JSON.stringify(recentActivities.value))
  } catch (error) {
    console.error('保存活动记录失败:', error)
  }
}

// 添加最近活动记录
const addRecentActivity = (moduleName) => {
  const now = new Date()
  const timeString = now.getHours().toString().padStart(2, '0') + ':' +
    now.getMinutes().toString().padStart(2, '0') + ':' +
    now.getSeconds().toString().padStart(2, '0')

  const activity = {
    name: `打开 ${moduleName}`,
    time: timeString
  }

  // 添加到数组开头
  recentActivities.value.unshift(activity)

  // 只保留最近5条记录
  if (recentActivities.value.length > 5) {
    recentActivities.value.splice(5)
  }

  // 保存到 sessionStorage
  saveRecentActivities()
}

const navigateTo = (path) => {
  // 记录最近活动
  const moduleName = moduleNames[path] || '未知功能'
  addRecentActivity(moduleName)

  router.push(path)
}

// 获取本地版本信息
const fetchLocalVersion = async () => {
  // 检查是否已有缓存且不是默认值
  if (localVersion.value.version !== '加载中...') {
    return
  }

  try {
    // 发送本地请求获取版本信息
    const response = await axios.get('http://127.0.0.1:8000/api/version/local')
    const versionData = {
      version: response.data.version || 'V2.5.1',
      status: response.data.status || '测试版本'
    }

    localVersion.value = versionData
    // 保存到 sessionStorage
    sessionStorage.setItem('localVersion', JSON.stringify(versionData))
  } catch (error) {
    console.error('获取本地版本信息失败:', error)
    const fallbackData = {
      version: 'V2.5.1',
      status: '测试版本'
    }
    localVersion.value = fallbackData
    sessionStorage.setItem('localVersion', JSON.stringify(fallbackData))
  }
}

// 获取远程版本信息
const fetchRemoteVersion = async () => {
  // 检查是否已有缓存且不是默认值
  if (remoteVersion.value.versionCode !== '加载中...') {
    return
  }

  try {
    const response = await axios.get('http://127.0.0.1:8000/api/get-remote-version')

    // 数据在 response.data 中
    const versionData = response.data
    const remoteData = {
      versionCode: versionData.versionCode || '未知',
      versionDesc: versionData.versionDesc || '暂无描述'
    }

    remoteVersion.value = remoteData
    // 保存到 sessionStorage
    sessionStorage.setItem('remoteVersion', JSON.stringify(remoteData))
  } catch (error) {
    const errorData = {
      versionCode: '获取失败',
      versionDesc: '网络连接异常'
    }
    remoteVersion.value = errorData
    sessionStorage.setItem('remoteVersion', JSON.stringify(errorData))
  }
}

// 操作手册功能
const openManual = async () => {
  // 记录最近活动
  addRecentActivity('操作手册')

  try {
    // 发送GET请求获取操作手册
    const response = await axios.post('http://127.0.0.1:8000/api/manual/guide')
    console.log('操作手册请求成功:')    // 在这里处理操作手册数据
  } catch (error) {
    console.error('操作手册请求失败:', error)
  }
}

// 组件挂载时获取版本信息
onMounted(() => {
  // 只有在没有缓存数据时才请求
  fetchLocalVersion()
  fetchRemoteVersion()
})
</script>

<style scoped>
.home-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  box-sizing: border-box;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 0 10px;
  width: 100%;
  max-width: 1200px;
}

.title {
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
  margin-bottom: 30px;
  width: 100%;
  max-width: 1200px;
  justify-items: center;
  place-content: center;
}

.module-card {
  background: white;
  border-radius: 16px;
  padding: 24px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
  width: 100%;
  min-width: 160px;
  max-width: 180px;
  position: relative;
  overflow: hidden;
}

.module-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.module-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

.module-card:hover::before {
  opacity: 1;
}

.module-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.module-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.icon-wrapper {
  font-size: 26px;
  color: white;
  z-index: 2;
  position: relative;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.module-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.module-icon:hover::before {
  opacity: 1;
}

/* 财务主题配色方案 */
.module-icon.statistics {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.module-icon.auto-voucher {
  background: linear-gradient(135deg, #4facfe 0%, #00c6ff 100%);
}

.module-icon.fund {
  background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
}

.module-icon.audit {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.module-icon.close {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.module-icon.tax {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.module-icon.quick-fill {
  background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
}

.module-icon.assistant {
  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
}

.module-icon.archive {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
}

.module-icon.sync {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.module-icon.settings {
  background: linear-gradient(135deg, #81ecec 0%, #00b894 100%);
}

.module-icon.others {
  background: linear-gradient(135deg, #a29bfe 0%, #74b9ff 100%);
}

.module-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #2c3e50;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.module-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
  line-height: 1.5;
  font-weight: 400;
}

.info-panels {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  width: 100%;
  max-width: 1200px;
}

.info-panel {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

.info-panel h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.info-panel ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-panel li {
  padding: 5px 0;
  color: #5a6c7d;
  font-size: 14px;
}

.software-info p {
  margin: 8px 0;
  color: #5a6c7d;
  font-size: 14px;
}

.version {
  color: #3498db;
  font-weight: 600;
}

.status {
  color: #27ae60;
  font-weight: 600;
}

.remote-version {
  color: #e74c3c;
  font-weight: 600;
}

.version-desc {
  color: #8e44ad;
  font-weight: 500;
  font-size: 13px;
}

.recent-activity {
  max-height: 120px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 4px;
}

.activity-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.activity-name {
  color: #5a6c7d;
  font-size: 13px;
  flex: 1;
  text-align: left;
}

.activity-time {
  color: #999;
  font-size: 11px;
  font-weight: 400;
  white-space: nowrap;
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .modules-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .modules-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .info-panels {
    grid-template-columns: 1fr;
  }
}
</style>