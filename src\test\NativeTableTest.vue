<template>
  <div class="native-table-test">
    <h2>原生表格组件测试</h2>
    
    <!-- 测试按钮 -->
    <div class="test-buttons">
      <button @click="addTestRow" class="test-btn">添加测试行</button>
      <button @click="deleteSelectedRows" class="test-btn">删除选中行</button>
      <button @click="exportToExcel" class="test-btn">导出Excel</button>
      <button @click="exportToCSV" class="test-btn">导出CSV</button>
      <button @click="resetData" class="test-btn">重置数据</button>
      <button @click="switchComponent" class="test-btn">
        切换到{{ useNative ? 'VTable' : '原生' }}组件
      </button>
    </div>
    
    <!-- 原生表格组件 -->
    <NativeTableComponent
      v-if="useNative"
      ref="nativeTableRef"
      :data="testData"
      :width="800"
      :height="400"
      :show-filter="true"
      :editable="true"
      :enable-copy-paste="true"
      :auto-width="true"
      :enable-excel-import="true"
      @data-change="handleDataChange"
      @cell-edit="handleCellEdit"
      @cell-select="handleCellSelect"
    />
    
    <!-- VTable组件对比 -->
    <VTableComponent
      v-else
      ref="vtableRef"
      :data="testData"
      :width="800"
      :height="400"
      :show-filter="true"
      :editable="true"
      :enable-copy-paste="true"
      :auto-width="true"
      :enable-excel-import="true"
      @data-change="handleDataChange"
      @cell-edit="handleCellEdit"
    />
    
    <!-- 数据变化日志 -->
    <div class="log-section">
      <h3>数据变化日志</h3>
      <div class="log-content">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-type">{{ log.type }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
    
    <!-- 当前数据展示 -->
    <div class="data-section">
      <h3>当前数据（{{ testData.length - 1 }} 行）</h3>
      <pre>{{ JSON.stringify(testData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import NativeTableComponent from '../components/NativeTableComponent.vue'
import VTableComponent from '../components/VTableComponent.vue'

// 响应式数据
const nativeTableRef = ref()
const vtableRef = ref()
const useNative = ref(true)
const logs = ref([])

// 测试数据
const testData = ref([
  ['姓名', '年龄', '城市', '工资', '入职日期'],
  ['张三', 28, '北京', 15000, '2023-01-15'],
  ['李四', 32, '上海', 18000, '2022-06-20'],
  ['王五', 25, '广州', 12000, '2023-03-10'],
  ['赵六', 35, '深圳', 22000, '2021-11-05']
])

// 添加日志
const addLog = (type, message) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    type,
    message
  })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 事件处理
const handleDataChange = (newData) => {
  testData.value = newData
  addLog('数据变化', `表格数据已更新，当前 ${newData.length - 1} 行`)
}

const handleCellEdit = (event) => {
  addLog('单元格编辑', `行${event.row} 列${event.col}: "${event.oldValue}" -> "${event.newValue}"`)
}

const handleCellSelect = (event) => {
  addLog('单元格选择', `选中 行${event.row} 列${event.col}`)
}

// 测试方法
const addTestRow = () => {
  const newRow = [
    `测试用户${Date.now()}`,
    Math.floor(Math.random() * 40) + 20,
    ['北京', '上海', '广州', '深圳'][Math.floor(Math.random() * 4)],
    Math.floor(Math.random() * 20000) + 10000,
    new Date().toISOString().slice(0, 10)
  ]
  
  testData.value.push(newRow)
  addLog('添加行', `添加了新的测试行`)
}

const deleteSelectedRows = () => {
  const tableRef = useNative.value ? nativeTableRef.value : vtableRef.value
  if (tableRef && tableRef.deleteRows) {
    tableRef.deleteRows()
    addLog('删除行', '执行删除选中行操作')
  }
}

const exportToExcel = () => {
  const tableRef = useNative.value ? nativeTableRef.value : vtableRef.value
  if (tableRef && tableRef.exportExcel) {
    tableRef.exportExcel()
    addLog('导出', '导出Excel文件')
  }
}

const exportToCSV = () => {
  const tableRef = useNative.value ? nativeTableRef.value : vtableRef.value
  if (tableRef && tableRef.exportCSV) {
    tableRef.exportCSV()
    addLog('导出', '导出CSV文件')
  }
}

const resetData = () => {
  testData.value = [
    ['姓名', '年龄', '城市', '工资', '入职日期'],
    ['张三', 28, '北京', 15000, '2023-01-15'],
    ['李四', 32, '上海', 18000, '2022-06-20'],
    ['王五', 25, '广州', 12000, '2023-03-10'],
    ['赵六', 35, '深圳', 22000, '2021-11-05']
  ]
  addLog('重置', '数据已重置为初始状态')
}

const switchComponent = () => {
  useNative.value = !useNative.value
  addLog('切换组件', `切换到${useNative.value ? '原生' : 'VTable'}组件`)
}

// 初始化日志
addLog('初始化', '测试页面已加载')
</script>

<style scoped>
.native-table-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.test-btn:hover {
  background: #66b1ff;
}

.log-section {
  margin-top: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 2px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  color: #909399;
  min-width: 80px;
}

.log-type {
  color: #409eff;
  min-width: 80px;
  font-weight: bold;
}

.log-message {
  color: #606266;
}

.data-section {
  margin-top: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
}

.data-section pre {
  background: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

h2 {
  color: #303133;
  margin-bottom: 20px;
}

h3 {
  color: #606266;
  margin-bottom: 10px;
  font-size: 16px;
}
</style>
