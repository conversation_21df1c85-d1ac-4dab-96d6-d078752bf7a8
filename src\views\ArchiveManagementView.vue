<template>
  <div class="archive-management-container">
    <!-- 单据下载 -->
    <div class="archive-section">
      <h2>单据下载</h2>
      <div class="archive-description">
        批量下载中台单据，先打开测试模式
      </div>
      <div class="button-group">
        <el-button type="success" @click="openChromeTest" :loading="chromeTestLoading">
          <el-icon>
            <ChromeFilled />
          </el-icon>
          {{ chromeTestLoading ? '启动中...' : '打开chrome测试器' }}
        </el-button>
        <el-button type="success" @click="exportMiddlePlatformData" :loading="middlePlatformLoading">
          <el-icon>
            <Download />
          </el-icon>
          {{ middlePlatformLoading ? '导出中...' : '中台单据导出' }}
        </el-button>
        <el-button type="primary" @click="insertDocumentScript" :loading="insertScriptLoading">
          <el-icon>
            <DocumentAdd />
          </el-icon>
          {{ insertScriptLoading ? '插入中...' : '插入单据脚本' }}
        </el-button>
      </div>
    </div>

    <!-- 批量打印单据 -->
    <div class="archive-section">
      <h2>批量打印单据</h2>
      <div class="archive-description">
        批量打印单据
      </div>
      <div class="button-group">
        <el-button type="success" @click="batchPrintDocuments" :loading="batchPrintLoading">
          <el-icon>
            <Printer />
          </el-icon>
          {{ batchPrintLoading ? '打印中...' : '批量打印单据' }}
        </el-button>
      </div>
    </div>

    <!-- 引入消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { ChromeFilled, Download, Printer, DocumentAdd } from '@element-plus/icons-vue'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

const chromeTestLoading = ref(false)
const middlePlatformLoading = ref(false)
const batchPrintLoading = ref(false)
const insertScriptLoading = ref(false)
const dialogVisible = ref(false)

// 打开chrome测试器
const openChromeTest = async () => {
  chromeTestLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '打开chrome浏览器',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('Chrome测试器启动任务已启动')
    } else {
      ElMessage.error('Chrome测试器启动失败')
    }
  } catch (error) {
    ElMessage.error('启动过程中发生错误: ' + error.message)
  } finally {
    chromeTestLoading.value = false
  }
}

// 中台单据导出
const exportMiddlePlatformData = async () => {
  middlePlatformLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '中台单据导出',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('中台单据导出任务已启动')
    } else {
      ElMessage.error('中台单据导出失败')
    }
  } catch (error) {
    ElMessage.error('导出过程中发生错误: ' + error.message)
  } finally {
    middlePlatformLoading.value = false
  }
}

// 插入单据脚本
const insertDocumentScript = async () => {
  insertScriptLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '插入单据脚本',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('插入单据脚本任务已启动')
    } else {
      ElMessage.error('插入单据脚本失败')
    }
  } catch (error) {
    ElMessage.error('插入过程中发生错误: ' + error.message)
  } finally {
    insertScriptLoading.value = false
  }
}

// 批量打印单据
const batchPrintDocuments = async () => {
  batchPrintLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '批量打印单据',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('批量打印单据任务已启动')
    } else {
      ElMessage.error('批量打印单据失败')
    }
  } catch (error) {
    ElMessage.error('打印过程中发生错误: ' + error.message)
  } finally {
    batchPrintLoading.value = false
  }
}
</script>

<style scoped>
.archive-management-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: auto;
  position: relative;
}

.archive-section {
  max-width: 800px;
  margin: 0 auto 40px auto;
  padding: 30px;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  background: #fafafa;
}

.archive-section h2 {
  text-align: left;
  margin-bottom: 15px;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.archive-description {
  color: #409eff;
  font-size: 14px;
  margin-bottom: 25px;
  line-height: 1.5;
}

.button-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.button-group .el-button {
  min-width: 160px;
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
}

.archive-section:last-of-type {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
  }

  .button-group .el-button {
    width: 100%;
  }
}
</style>