# 薪酬个税系统修改说明

## 修改概述

根据需求，对 `src/views/SalaryTax2View.vue` 进行了以下主要修改：

### 1. 导入功能修改
- **原功能**: 导入Excel到当前工作表
- **新功能**: 导入专项附加扣除数据
- **修改内容**:
  - 将按钮文本从"导入Excel"改为"导入专项附加"
  - 修改触发函数从 `triggerFileImport` 改为 `triggerSpecialDeductionImport`
  - 修改处理函数从 `handleFileImport` 改为 `handleSpecialDeductionImport`

### 2. 专项附加扣除导入功能
- **新增函数**: `importSpecialDeductionFromExcel(file: File, importMode: 'overwrite' | 'append')`
- **功能特点**:
  - 正确处理身份证号列，确保以文本格式存储
  - 支持覆盖导入和追加导入两种模式
  - 自动识别"累计专项附加扣除表"工作表
  - 保持表头格式和样式

### 3. 导入模式选择
- **新增函数**: `selectImportMode()`
- **功能**: 弹出对话框让用户选择导入模式
- **选项**:
  - **覆盖导入**: 清空现有数据，使用新数据替换
  - **追加导入**: 保留现有数据，在末尾添加新数据

### 4. 导出功能修改
- **原功能**: 导出当前表格到Excel
- **新功能**: 计算社保公积金回摊
- **修改内容**:
  - 将按钮文本从"导出当前表格"改为"计算社保公积金回摊"
  - 修改触发函数从 `exportCurrentSheetToExcel` 改为 `calculateSocialSecurityAllocation`

### 5. 社保公积金回摊计算
- **新增函数**: `calculateSocialSecurityAllocation()`
- **功能流程**:
  1. 获取"算税底稿"工作表数据
  2. 获取"社保公积金实际缴纳表"工作表数据
  3. 发送数据到后台API进行回摊计算
  4. 接收回摊结果并更新"薪酬总表(无一次性年终及包干)"工作表
  5. 正确处理身份证号列格式

### 6. 构建财务模板功能
- **修改函数**: `buildFinanceTemplate()`
- **功能流程**:
  1. 获取"薪酬总表(无一次性年终及包干)"工作表数据
  2. 发送数据到后台API构建财务一体化模板
  3. 按月份和缴纳单位汇总社保公积金数据
  4. 生成财务一体化计提发放模板

## 后端API修改

### 1. 新增API端点
- **路径1**: `/api/calculate-social-security-allocation`
- **方法**: POST
- **功能**: 计算社保公积金回摊

- **路径2**: `/api/build-finance-template`
- **方法**: POST
- **功能**: 构建财务一体化计提发放模板

### 2. 请求模型
```typescript
interface SocialSecurityAllocationRequest {
  taxCalculationData: any[][];     // 算税底稿数据
  socialSecurityData: any[][];     // 社保公积金实际缴纳表数据
  year: string;                    // 年份
}

interface BuildFinanceTemplateRequest {
  adjustmentData: any[][];         // 薪酬总表数据
  year: string;                    // 年份
  templateType: string;            // 模板类型
}
```

### 3. 响应模型
```typescript
interface SocialSecurityAllocationResponse {
  code: number;
  message: string;
  data?: any[][];                  // 回摊计算结果
  timestamp: string;
}

interface BuildFinanceTemplateResponse {
  code: number;
  message: string;
  data?: {                         // 财务模板数据
    template_type: string;
    year: string;
    summary: object;
    total_records: number;
    generated_at: string;
  };
  timestamp: string;
}
```

### 4. 计算逻辑
- 解析算税底稿和社保公积金数据的表头
- 根据月份和缴纳单位建立映射关系
- 计算个人回摊金额（养老、公积金、医疗、失业、年金）
- 计算单位分摊金额（养老、公积金、医疗、失业、工伤、生育、补充医疗、年金）
- 计算总成本（收入 + 个人回摊 + 单位分摊 + 个税）
- 返回完整的回摊数据表

## 数据处理特点

### 1. 身份证号处理
- 自动识别包含"身份证号"或"证件号码"的列
- 强制转换为文本格式，防止Excel自动转换为数字
- 对纯数字身份证号添加单引号前缀

### 2. 数据验证
- 检查必要的表头字段是否存在
- 验证数据行数是否足够
- 处理空值和异常数据

### 3. 工作表管理
- 自动调整工作表大小以适应数据
- 设置表头样式（粗体、背景色）
- 清除旧数据后设置新数据

## 使用流程

### 专项附加扣除导入
1. 点击"导入专项附加"按钮
2. 选择Excel文件
3. 选择导入模式（覆盖或追加）
4. 系统自动处理身份证号格式并导入到"累计专项附加扣除表"

### 社保公积金回摊计算
1. 确保"算税底稿"和"社保公积金实际缴纳表"有数据
2. 点击"计算社保公积金回摊"按钮
3. 系统发送数据到后台计算
4. 结果自动更新到"薪酬总表(无一次性年终及包干)"工作表

### 构建财务一体化模板
1. 确保已完成社保公积金回摊计算
2. 点击"构建财务一体化计提发放模板(社保公积金表)"按钮
3. 系统汇总薪酬总表数据按月份和缴纳单位分类
4. 生成财务一体化计提发放模板数据

## 注意事项

1. **数据格式**: 确保Excel文件的表头与系统预期格式一致
2. **身份证号**: 系统会自动处理身份证号的文本格式问题
3. **网络连接**: 回摊计算需要后台API支持，确保网络连接正常
4. **数据完整性**: 计算前请确保相关工作表数据完整

## 技术实现

- **前端框架**: Vue 3 + TypeScript
- **表格组件**: Univer
- **Excel处理**: ExcelJS
- **后端框架**: FastAPI + Python
- **数据处理**: Pandas (后端)
