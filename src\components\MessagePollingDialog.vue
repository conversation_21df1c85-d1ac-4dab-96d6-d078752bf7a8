<template>
  <el-dialog v-model="visible" title="执行进展" width="500px" :close-on-click-modal="false" :close-on-press-escape="false">
    <div class="message-container">
      <div v-if="!message" class="empty-message">
        等待消息...
      </div>
      <div v-else class="message-content" ref="messageContentRef">
        <pre class="message-text">{{ message }}</pre>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button type="danger" @click="stopPolling" :loading="isStoppingFunction" :disabled="!pollingInterval">
          {{ isStoppingFunction ? '正在停止...' : '停止' }}
        </el-button>
        <el-button type="primary" @click="closeDialog">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, onUnmounted, nextTick } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = ref(props.modelValue)
const message = ref('')
const pollingInterval = ref(null)
const messageContentRef = ref(null)
const isStoppingFunction = ref(false)
const lastMessage = ref('') // 添加这一行来存储上一次的消息

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal) {
      startPolling()
    } else {
      stopPolling()
    }
  }
)

// 监听visible变化
watch(
  () => visible.value,
  (newVal) => {
    emit('update:modelValue', newVal)
    if (!newVal) {
      stopPolling()
    }
  }
)

// 开始轮询
const startPolling = async () => {
  // 清空之前的消息
  message.value = ''

  // 重置停止标志
  await resetStopFlag()

  // 开始轮询，每0.1秒获取一次消息
  pollingInterval.value = setInterval(async () => {
    try {
      const newMessage = await fetchMessage()
      // 只有消息变化时才更新
      if (newMessage !== lastMessage.value && newMessage !== 'not need refresh' ) {
        // 每次都清空旧消息，显示新的完整消息
        message.value = newMessage
        lastMessage.value = newMessage

        // 自动滚动到最后一行
        await nextTick()
        scrollToBottom()
      }
    } catch (error) {
      console.error('获取消息失败:', error)
      message.value = '获取消息失败: ' + error.message
    }
  }, 100)
}

// 从后端获取消息
const fetchMessage = async () => {
  try {
    const response = await fetch('http://127.0.0.1:8000/api/get-message')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    // 检查响应是否为JSON格式
    const contentType = response.headers.get('content-type')
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json()
      // 如果返回的是包含message字段的对象
      if (data && typeof data === 'object' && 'message' in data) {
        return data.message
      }
      // 如果返回的是直接的字符串
      return JSON.stringify(data)
    } else {
      // 如果是文本格式，直接返回文本内容
      const text = await response.text()
      return text
    }
  } catch (error) {
    throw new Error(`获取消息失败: ${error.message}`)
  }
}

// 停止后端函数执行
const stopFunction = async () => {
  try {
    const response = await fetch('http://127.0.0.1:8000/api/stop-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const result = await response.json()
    return result
  } catch (error) {
    console.error('停止函数失败:', error)
    throw error
  }
}

// 重置停止标志
const resetStopFlag = async () => {
  try {
    await fetch('http://127.0.0.1:8000/api/reset-stop-flag', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error('重置停止标志失败:', error)
  }
}

// 滚动到最后一行
const scrollToBottom = () => {
  if (messageContentRef.value) {
    messageContentRef.value.scrollTop = messageContentRef.value.scrollHeight
  }
}

// 停止轮询
const stopPolling = async () => {
  isStoppingFunction.value = true

  if (pollingInterval.value) {
    clearInterval(pollingInterval.value)
    pollingInterval.value = null
  }

  // 调用后端停止函数
  try {
    await stopFunction()
    message.value += '\n\n--- 已发送停止信号 ---'
    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('发送停止信号失败:', error)
    message.value += '\n\n--- 停止信号发送失败 ---'
    await nextTick()
    scrollToBottom()
  } finally {
    isStoppingFunction.value = false
  }
}

// 关闭对话框
const closeDialog = async () => {
  // 在关闭时也调用停止函数
  if (pollingInterval.value) {
    await stopPolling()
  }
  visible.value = false
}

// 组件卸载时清理
onUnmounted(() => {
  if (pollingInterval.value) {
    clearInterval(pollingInterval.value)
    pollingInterval.value = null
  }
})
</script>

<style scoped>
.message-container {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #f5f7fa;
}

.empty-message {
  text-align: center;
  color: #909399;
  font-style: italic;
  line-height: 200px;
}

.message-content {
  max-height: 380px;
  overflow-y: auto;
  padding: 0;
}

.message-text {
  margin: 0;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #606266;
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
