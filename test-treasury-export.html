<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>司库在途支付导出功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .export-section {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            background: #f0f8ff;
            border-radius: 6px;
            border: 1px solid #d1ecf1;
            margin: 20px 0;
            gap: 10px;
        }
        input[type="date"] {
            width: 140px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 8px 16px;
            background: #17a2b8;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #138496;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>司库在途支付导出功能测试</h1>
        
        <div class="export-section">
            <input type="date" id="exportStartDate" placeholder="导出开始日期">
            <input type="date" id="exportEndDate" placeholder="导出结束日期">
            <button id="exportBtn" onclick="testExport()">司库在途支付导出</button>
        </div>
        
        <div id="result"></div>
        
        <h2>功能说明</h2>
        <ul>
            <li>选择开始日期和结束日期</li>
            <li>点击"司库在途支付导出"按钮</li>
            <li>系统会调用后端API生成Excel文件</li>
            <li>浏览器会自动下载生成的文件</li>
        </ul>
        
        <h2>测试步骤</h2>
        <ol>
            <li>确保后端服务已启动 (python -m uvicorn backend.main:app --reload)</li>
            <li>选择合适的日期范围</li>
            <li>点击导出按钮</li>
            <li>检查是否成功下载Excel文件</li>
        </ol>
        
        <h2>API接口信息</h2>
        <p><strong>URL:</strong> http://127.0.0.1:8000/api/treasury-payment-export</p>
        <p><strong>方法:</strong> POST</p>
        <p><strong>请求体:</strong></p>
        <pre>{
  "start_date": "2024-05-01",
  "end_date": "2024-05-31"
}</pre>
    </div>

    <script>
        // 设置默认日期
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            
            document.getElementById('exportStartDate').value = firstDay.toISOString().split('T')[0];
            document.getElementById('exportEndDate').value = today.toISOString().split('T')[0];
        });

        async function testExport() {
            const startDate = document.getElementById('exportStartDate').value;
            const endDate = document.getElementById('exportEndDate').value;
            const resultDiv = document.getElementById('result');
            const exportBtn = document.getElementById('exportBtn');
            
            // 清除之前的结果
            resultDiv.innerHTML = '';
            
            // 验证日期
            if (!startDate || !endDate) {
                showResult('请选择导出开始日期和结束日期', 'error');
                return;
            }
            
            if (new Date(startDate) > new Date(endDate)) {
                showResult('开始日期不能晚于结束日期', 'error');
                return;
            }
            
            try {
                // 禁用按钮并显示加载状态
                exportBtn.disabled = true;
                exportBtn.textContent = '导出中...';
                showResult('正在导出司库在途支付数据...', 'info');
                
                const response = await fetch('http://127.0.0.1:8000/api/treasury-payment-export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        start_date: startDate,
                        end_date: endDate
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                // 检查响应类型
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')) {
                    // 处理Excel文件下载
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `司库在途支付_${startDate}_${endDate}.xlsx`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    showResult('司库在途支付导出成功！文件已开始下载。', 'success');
                } else {
                    // 处理JSON响应
                    const result = await response.json();
                    if (result.success) {
                        showResult('司库在途支付导出成功！', 'success');
                    } else {
                        throw new Error(result.message || '司库在途支付导出失败');
                    }
                }
                
            } catch (error) {
                console.error('司库在途支付导出失败:', error);
                showResult('司库在途支付导出失败: ' + error.message, 'error');
            } finally {
                // 恢复按钮状态
                exportBtn.disabled = false;
                exportBtn.textContent = '司库在途支付导出';
            }
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
    </script>
</body>
</html>