<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UniversalTableComponent 导出功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: #4472C4;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .content {
            padding: 20px;
        }
        .test-info {
            background: #e3f2fd;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #1976D2;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✅ ";
            color: #4CAF50;
            font-weight: bold;
        }
        .usage-example {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .note strong {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 UniversalTableComponent 导出功能测试</h1>
            <p>基于 ExcelJS 的高级 Excel 导出功能</p>
        </div>
        
        <div class="content">
            <div class="test-info">
                <h3>📋 功能特性</h3>
                <ul class="feature-list">
                    <li>支持多工作表导出</li>
                    <li>自动处理 Univer 单元格对象格式</li>
                    <li>自定义文件名和样式</li>
                    <li>表头样式设置（字体、颜色、对齐）</li>
                    <li>自动列宽调整</li>
                    <li>单元格边框样式</li>
                    <li>选择性工作表导出</li>
                    <li>完整的错误处理和加载状态</li>
                </ul>
            </div>

            <div class="test-info">
                <h3>🔧 基础用法</h3>
                <div class="usage-example">
// 基础导出 - 导出所有工作表
await tableRef.value.exportData()
                </div>
            </div>

            <div class="test-info">
                <h3>⚙️ 高级用法</h3>
                <div class="usage-example">
// 自定义导出选项
const options = {
  filename: '自定义文件名',
  includeSheets: ['工作表1', '工作表2'], // 指定导出的工作表
  headerStyle: {
    font: { bold: true, color: { argb: 'FFFFFFFF' } },
    fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } },
    alignment: { horizontal: 'center', vertical: 'middle' }
  },
  addBorders: true,
  autoFitColumns: true,
  maxColumnWidth: 50,
  minColumnWidth: 10
}

const result = await tableRef.value.exportDataWithOptions(options)
if (result.success) {
  console.log(`导出成功: ${result.filename}`)
}
                </div>
            </div>

            <div class="test-info">
                <h3>📝 Vue 组件集成示例</h3>
                <div class="usage-example">
&lt;template&gt;
  &lt;div&gt;
    &lt;button @click="basicExport"&gt;基础导出&lt;/button&gt;
    &lt;button @click="customExport"&gt;自定义导出&lt;/button&gt;
    
    &lt;UniversalTableComponent
      ref="tableRef"
      :initial-data="tableData"
      workbook-name="我的表格"
      @error="handleError"
    /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue'
import UniversalTableComponent from './UniversalTableComponent.vue'

const tableRef = ref(null)

const basicExport = async () =&gt; {
  await tableRef.value.exportData()
}

const customExport = async () =&gt; {
  const options = {
    filename: `数据导出_${new Date().toLocaleDateString()}`,
    headerStyle: {
      font: { bold: true, color: { argb: 'FFFFFFFF' } },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } }
    }
  }
  
  await tableRef.value.exportDataWithOptions(options)
}
&lt;/script&gt;
                </div>
            </div>

            <div class="note">
                <strong>📌 注意事项：</strong>
                <ul>
                    <li>需要现代浏览器支持 Blob 和 URL.createObjectURL API</li>
                    <li>大量数据可能影响导出性能，建议分批导出</li>
                    <li>导出完成后会自动清理 URL 对象，避免内存泄漏</li>
                    <li>文件名会自动添加时间戳避免冲突</li>
                    <li>自动处理 Univer 单元格对象格式转换</li>
                </ul>
            </div>

            <div class="test-info">
                <h3>🎯 测试步骤</h3>
                <ol>
                    <li>在项目中导入 UniversalTableComponent</li>
                    <li>准备测试数据（多工作表格式）</li>
                    <li>调用 exportData() 进行基础导出测试</li>
                    <li>调用 exportDataWithOptions() 进行高级导出测试</li>
                    <li>验证生成的 Excel 文件格式和内容</li>
                    <li>测试错误处理机制</li>
                </ol>
            </div>

            <div class="test-info">
                <h3>📊 示例数据结构</h3>
                <div class="usage-example">
const sampleData = {
  '员工信息': [
    ['姓名', '工号', '部门', '职位'],
    ['张三', '001', '技术部', '前端工程师'],
    ['李四', '002', '技术部', '后端工程师']
  ],
  '部门统计': [
    ['部门', '人数', '平均薪资'],
    ['技术部', 15, 12000],
    ['产品部', 8, 10000]
  ]
}
                </div>
            </div>
        </div>
    </div>
</body>
</html>